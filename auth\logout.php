<?php
header('Content-Type: application/json');
session_start();

// إذا موجودة الجلسة، نمسح بيانات المستخدم
if (isset($_SESSION['user'])) {
    unset($_SESSION['user']); // مسح بيانات المستخدم
}

// تدمير الجلسة بالكامل
session_destroy();

// حذف كوكي الـ PHPSESSID من المتصفح
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// رد للعميل
echo json_encode([
    'status' => 'success',
    'message' => 'Logged out successfully'
]);
