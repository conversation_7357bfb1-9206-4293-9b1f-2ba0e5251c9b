<?php
header('Content-Type: application/json');

require_once '../config/database.php';
session_start(); // مهم عشان نتحقق من المستخدم

// تحقق إذا المستخدم مسجل دخول
if (!isset($_SESSION['user'])) {
    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => 'Unauthorized. Please login first.'
    ]);
    exit;
}

$con = dbConnect();

// اقرأ البيانات القادمة كـ JSON
$input = json_decode(file_get_contents('php://input'), true);

$full_name   = isset($input['full_name']) ? trim($input['full_name']) : '';
$phone       = isset($input['phone']) ? trim($input['phone']) : '';
$balance     = isset($input['balance']) ? (int)$input['balance'] : 0;
$description = isset($input['description']) ? trim($input['description']) : '';

if ($full_name === '' || $phone === '') {
    echo json_encode([
        'status' => 'error',
        'message' => 'Full name and phone are required'
    ]);
    exit;
}

// جهز استعلام آمن
$stmt = $con->prepare("INSERT INTO customers (full_name, phone, balance, description) VALUES (?, ?, ?, ?)");
$stmt->bind_param("ssis", $full_name, $phone, $balance, $description);

if ($stmt->execute()) {
    echo json_encode([
        'status' => 'success',
        'message' => 'Customer added successfully',
        'customer' => [
            'id' => $stmt->insert_id,
            'full_name' => $full_name,
            'phone' => $phone,
            'balance' => $balance,
            'description' => $description
        ]
    ]);
} else {
    echo json_encode([
        'status' => 'error',
        'message' => 'Failed to add customer: ' . $stmt->error
    ]);
}

$stmt->close();
$con->close();
