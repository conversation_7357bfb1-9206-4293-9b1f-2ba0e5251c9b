<?php
header('Content-Type: application/json');

require_once '../config/database.php';

session_start(); // ⚡ أهم سطر لبدء الجلسة

$con = dbConnect();

// اقرأ البيانات القادمة كـ JSON
$input = json_decode(file_get_contents('php://input'), true);
$email = isset($input['email']) ? trim($input['email']) : '';
$password = isset($input['password']) ? trim($input['password']) : '';

if ($email === '' || $password === '') {
    echo json_encode([
        'status' => 'error',
        'message' => 'Email and password are required'
    ]);
    exit;
}

// جهز استعلام آمن (prepared statement)
$stmt = $con->prepare("SELECT id,email, full_name, password, role FROM employees WHERE email = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode([
        'status' => 'error',
        'message' => 'User not found'
    ]);
    exit;
}

$user = $result->fetch_assoc();

// تحقق من كلمة السر
if (!password_verify($password, $user['password'])) {
    echo json_encode([
        'status'=>'error',
        'message'=>'Invalid password'
    ]);
    exit;
}

// ⚡ إنشاء الجلسة وتخزين بيانات المستخدم
session_regenerate_id(true); // تجديد ID الجلسة لأمان أفضل

$_SESSION['user'] = [
  'id' => $user['id'],
  'full_name' => $user['full_name'],
  'email' => $email,
  'role' => $user['role']
];

// رد للعميل
echo json_encode([
    'status'=>'success',
    'user'=>[
        'id'=>$user['id'],
        'full_name'=>$user['full_name'],
        'email'=>$user['email'],
        'role'=>$user['role']
    ]
]);

$stmt->close();
$con->close();
